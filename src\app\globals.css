@import 'tailwindcss';
@plugin "@iconify/tailwind4" {
  prefix: "solar";
}
@plugin "daisyui" {
  themes: false;
}

@plugin "daisyui/theme" {
  name: 'material-light';
  default: true;
  prefersdark: false;
  color-scheme: light;

  --root-bg: #fdfeff;
  --layout-sidebar-background: #f5f7ff;
  --layout-topbar-background: #f5f7ff;
  --color-base-100: #f6f8ff;
  --color-base-200: #eaecfa;
  --color-base-300: #e0e2f8;
  --color-base-content: #191e28;
  --color-primary: #167bff;
  --color-primary-content: #fff;
  --color-secondary: #9c5de8;
  --color-secondary-content: #fff;
  --color-accent: #00d3bb;
  --color-accent-content: #f3fbf6;
  --color-neutral: #1e2832;
  --color-neutral-content: #fafcff;
  --color-info: #14b4ff;
  --color-info-content: #fff;
  --color-success: #0bbf58;
  --color-success-content: #fff;
  --color-warning: #f5a524;
  --color-warning-content: #150a00;
  --color-error: #f31260;
  --color-error-content: #fff;

  --radius-field: 20px;
  --radius-box: 20px;
  --rounded-box: 20px;
  --radius-selector: 20px;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --card-p: 20px;
  --depth: 0;
  --noise: 0;
}

@plugin "daisyui/theme" {
  name: 'material-dark';
  default: false;
  prefersdark: true;
  color-scheme: dark;

  --root-bg: #141618;
  --layout-sidebar-background: #181c20;
  --layout-topbar-background: #181c22;
  --color-base-100: #181e24;
  --color-base-200: #202830;
  --color-base-300: #2c323a;
  --color-base-content: #f0f4f8;
  --color-primary: #167bff;
  --color-primary-content: #fff;
  --color-secondary: #9c5de8;
  --color-secondary-content: #fff;
  --color-accent: #00d3bb;
  --color-accent-content: #f3fbf6;
  --color-neutral: #dce1e6;
  --color-neutral-content: #1e2832;
  --color-info: #14b4ff;
  --color-info-content: #fff;
  --color-success: #0bbf58;
  --color-success-content: #fff;
  --color-warning: #f5a524;
  --color-warning-content: #150a00;
  --color-error: #f31260;
  --color-error-content: #fff;
  
  --radius-field: 20px;
  --radius-box: 20px;
  --rounded-box: 20px;
  --radius-selector: 20px;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --card-p: 20px;
  --depth: 0;
  --noise: 0;
}
