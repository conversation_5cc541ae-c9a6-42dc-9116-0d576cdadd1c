import Image from 'next/image'
import Link from 'next/link'

export default function LoginPage() {
  return (
    <div className="grid grid-cols-12 overflow-auto h-screen">
      <div className="relative hidden col-span-6 lg:block">
        <Image src="/placeholder.svg" alt="placeholder" fill priority />
      </div>
      <div className="col-span-12 lg:col-span-6 xl:col-span-4">
        <div className="flex flex-col items-stretch md:p-8 p-6 lg:p-16">
          <div className="flex items-center justify-between relative">
            <Link href="/">
              <Image src="/logo-light.svg" alt="logo" width={120} height={32} />
            </Link>
            <div className="dropdown dropdown-end">
              <div
                tabIndex={0}
                role="button"
                className="btn btn-circle btn-outline border-base-300"
                aria-label="Theme toggle"
              >
                <span className="icon-[solar--sun-2-bold-duotone] size-4 inline"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
